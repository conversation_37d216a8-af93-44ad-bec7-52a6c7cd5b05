{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8c60413e-c459-4442-b528-c8e68d14471a", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "bc20533b-a6e3-4ea8-add7-5421032b1d98", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}